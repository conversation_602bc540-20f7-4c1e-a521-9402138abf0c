package com.saida;

import com.saida.common.utils.PrintUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

@SpringBootApplication
public class SinglebootApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(SinglebootApplication.class, args);
        Environment environment = run.getBean(Environment.class);
        PrintUtil.print(environment.getProperty("server.servlet.context-path"), environment.getProperty("server.port"));

    }

}
