package com.saida.common.core.domain.bo;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮箱登录对象
 *
 * <AUTHOR>
 */

@Data
public class XcxLoginBody implements Serializable {
    private static final long serialVersionUID = 1L;

    private String code;

    private String encryptedData;

    private String iv;
}