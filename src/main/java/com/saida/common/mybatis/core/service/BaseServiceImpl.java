package com.saida.common.mybatis.core.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.common.mybatis.enums.ResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

@Slf4j
public class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IBaseService<T> {

    @Override
    public T getById(Serializable id) {
        return super.getById(Assert.notNull(id, ResultEnum.PARAM_ERROR.getDesc()));
    }

    @Override
    public T getByIdOrThrow(Serializable id) {
        return Assert.notNull(getById(id), ResultEnum.PARAM_ERROR.getDesc());
    }

    @Override
    public T getByIdOrThrow(Serializable id, String errorMsgTemplate, Object... params) {
        return Assert.notNull(getById(id), errorMsgTemplate, params);
    }

    @Override
    public T getByIdOrThrow(Serializable id, Class<T> clazz, String... ignoreProperties) {
        return BeanUtil.copyProperties(getByIdOrThrow(id), clazz, ignoreProperties);
    }

    @Override
    public T getByIdOrNewInstance(Serializable id, Class<T> clazz) {
        T entity = getById(id);
        return entity != null ? entity : ReflectUtil.newInstanceIfPossible(clazz);
    }

    @Override
    public List<T> listByIdsOrThrow(Collection<? extends Serializable> ids) {
        return Assert.notEmpty(listByIds(Assert.notEmpty(ids)), ResultEnum.PARAM_ERROR.getDesc());
    }

    @Override
    public boolean saveBatch(Collection<T> entityList) {
        return !CollectionUtils.isEmpty(entityList) && super.saveBatch(entityList);
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<T> entityList) {
        return !CollectionUtils.isEmpty(entityList) && super.saveOrUpdateBatch(entityList);
    }

    @Override
    public boolean updateBatchById(Collection<T> entityList) {
        return !CollectionUtils.isEmpty(entityList) && super.updateBatchById(entityList);
    }

    @Override
    public boolean saveOrUpdate(T entity) {
        BeanUtil.trimStrFields(entity);
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean removeById(Serializable id) {
        return super.removeById(Assert.notNull(id, ResultEnum.PARAM_ERROR.getDesc()));
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        return super.removeByIds(Assert.notEmpty(list, ResultEnum.PARAM_ERROR.getDesc()));
    }
}