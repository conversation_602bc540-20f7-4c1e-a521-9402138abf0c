package com.saida.common.mybatis.core.service;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.common.mybatis.enums.ResultEnum;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface IBaseService<T> extends IService<T> {

    /**
     * 根据 ID 查询
     *
     * @param id 主键id
     * @return 无数据抛异常
     */
    T getByIdOrThrow(Serializable id);

    T getByIdOrThrow(Serializable id, String errorMsgTemplate, Object... params);

    /**
     * 根据 ID 查询,复制Bean对象属性
     *
     * @param id               主键id
     * @param clazz            copy的class
     * @param ignoreProperties 忽略的属性
     * @return 无数据抛异常
     */
    T getByIdOrThrow(Serializable id, Class<T> clazz, String... ignoreProperties);

    /**
     * 根据 ID 查询
     *
     * @param id    主键id
     * @param clazz 被构造的类
     * @return 无数据返回构造后的对象
     */
    T getByIdOrNewInstance(Serializable id, Class<T> clazz);

    /**
     * 查询一条记录  LIMIT 1
     */
    default T getAny(LambdaQueryWrapper<T> queryWrapper) {
        return getOne(queryWrapper.last("LIMIT 1"));
    }

    /**
     * 查询一条记录
     */
    default T getAnyOrThrow(LambdaQueryWrapper<T> queryWrapper) {
        return Assert.notNull(getAny(queryWrapper), ResultEnum.PARAM_ERROR.getDesc());
    }

    /**
     * 查询（根据ID 批量查询）
     *
     * @param ids 主键di集合
     * @return 无数据抛异常
     */
    List<T> listByIdsOrThrow(Collection<? extends Serializable> ids);
}