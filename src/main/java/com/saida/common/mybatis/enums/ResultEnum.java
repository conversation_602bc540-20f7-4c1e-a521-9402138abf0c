package com.saida.common.mybatis.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ResultEnum {
    /**
     *  成功
     */
    SUCCESS(200, "成功"),

    ENCRYSUCCESS(201, "成功"),

    PARAM_ERROR(400, "参数异常"),

    METHOD_ERROR(401, "请求方式错误"),

    ERROR(4000, "失败"),

    UNAUTHENTICATED(401, "请先通过身份认证"), AUTH_FAIL(1400, "认证失败"),

    // token异常
    TOKEN_PAST(1401, "身份过期，请求重新登录！"), TOKEN_ERROR(1402, "令牌错误"),

    HEADER_ERROR(1403, "请求头错误"),

    delete_confirm(1409, "是否确认删除！"),


    AUTH_USERNAME_NONE(1405, "用户名不能为空"), AUTH_PASSWORD_NONE(1406, "密码不能为空"),

    MENU_NO(306, "没此权限，请联系管理员！");

    private final Integer code;
    private final String desc;
}