package com.saida.common.translation.core.impl;

import com.saida.common.core.service.DictService;
import com.saida.common.core.utils.StringUtils;
import com.saida.common.translation.annotation.TranslationType;
import com.saida.common.translation.constant.TransConstant;
import com.saida.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * 字典翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.DICT_TYPE_TO_LABEL)
public class DictTypeTranslationImpl implements TranslationInterface<String> {

    private final DictService dictService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String dictValue && StringUtils.isNotBlank(other)) {
            return dictService.getDictLabel(other, dictValue);
        }
        return null;
    }
}
