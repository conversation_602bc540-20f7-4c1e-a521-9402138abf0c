package com.saida.common.utils;

public class PrintUtil {

    public static void print(String contextPath, String serverPort) {
        System.out.println("        *************       *************");
        System.out.println("     ***************************************");
        System.out.println("   *******************************************");
        System.out.println("  *********************************************");
        System.out.println("  *********************************************");
        System.out.println("  *********************************************");
        System.out.println("   *******************************************");
        System.out.println("    *****************************************");
        System.out.println("     ***************************************");
        System.out.println("       ***********************************");
        System.out.println("         *******************************");
        System.out.println("            *************************");
        System.out.println("                *****************");
        System.out.println("                    *********");
        System.out.println("                       ***");
        System.out.printf("Context Path=【%s】, Server Port=【%s】%n", contextPath, serverPort);
        System.out.println("启动路径" + System.getProperty("user.dir"));
//        try {
//            String jarPath = new File(PrintUtil.class
//                    .getProtectionDomain()
//                    .getCodeSource()
//                    .getLocation()
//                    .toURI())
//                    .getAbsolutePath();
//            System.out.println("JAR 文件路径：" + jarPath);
//        } catch (URISyntaxException e) {
//            System.out.println("获取 JAR 文件路径失败：" + e.getMessage());
//        }
        System.out.println("<<<<<<<<<<<<<<<<<<< 服务启动完成 >>>>>>>>>>>>>>>>>>>");
    }
}