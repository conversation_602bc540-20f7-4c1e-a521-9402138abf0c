package com.saida.common.utils;

public class PrintUtil {

    public static void print(String contextPath, String serverPort) {
        System.out.println("        *************       *************");
        System.out.println("     ***************************************");
        System.out.println("   *******************************************");
        System.out.println("  *********************************************");
        System.out.println("  *********************************************");
        System.out.println("  *********************************************");
        System.out.println("   *******************************************");
        System.out.println("    *****************************************");
        System.out.println("     ***************************************");
        System.out.println("       ***********************************");
        System.out.println("         *******************************");
        System.out.println("            *************************");
        System.out.println("                *****************");
        System.out.println("                    *********");
        System.out.println("                       ***");
        System.out.printf("Context Path=【%s】, Server Port=【%s】%n", contextPath, serverPort);
        System.out.println("启动路径" + System.getProperty("user.dir"));
        System.out.println("<<<<<<<<<<<<<<<<<<< 服务启动完成 >>>>>>>>>>>>>>>>>>>");
    }
}