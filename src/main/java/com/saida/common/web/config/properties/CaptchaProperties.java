package com.saida.common.web.config.properties;

import com.saida.common.web.enums.CaptchaCategory;
import com.saida.common.web.enums.CaptchaType;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 验证码 配置属性
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@ConfigurationProperties(prefix = "captcha")
public class CaptchaProperties {

    private Boolean enable;

    /**
     * 验证码类型
     */
    private CaptchaType type;

    /**
     * 验证码类别
     */
    private CaptchaCategory category;

    /**
     * 数字验证码位数
     */
    private Integer numberLength;

    /**
     * 字符验证码长度
     */
    private Integer charLength;

    @PostConstruct
    public void init() {
        log.info("初始化验证码配置：{}", this);
    }
}
