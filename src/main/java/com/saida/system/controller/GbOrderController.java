package com.saida.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.saida.common.core.domain.R;
import com.saida.common.mybatis.core.page.PageQuery;
import com.saida.common.mybatis.core.page.TableDataInfo;
import com.saida.common.web.core.BaseController;
import com.saida.system.domain.dto.CreateOrderDTO;
import com.saida.system.domain.dto.GetRegionDTO;
import com.saida.system.domain.dto.VerifyCodeDTO;
import com.saida.system.domain.entity.GbOrderEntity;
import com.saida.system.domain.vo.CreateOrderVO;
import com.saida.system.domain.vo.GbOrderListVO;
import com.saida.system.domain.vo.GetRegionVO;
import com.saida.system.service.GbOrderService;
import com.saida.system.ty.config.TyVisualConfig;
import com.saida.system.ty.enums.TyVisualReqEnum;
import com.saida.system.ty.pojo.req.QueryOrderStatusReq;
import com.saida.system.ty.pojo.resp.QueryOrderStatusResp;
import com.saida.system.ty.pojo.resp.TyVisualResp;
import com.saida.system.ty.util.TySendRequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/gb/order")
public class GbOrderController extends BaseController {

    private final GbOrderService gbOrderService;
    private final TySendRequestUtil tySendRequestUtil;
    private final TyVisualConfig tyVisualConfig;

    @GetMapping("/getOrderListLast12")
    public TableDataInfo<GbOrderListVO> getOrderListLast12(PageQuery pageQuery) {
        return gbOrderService.getOrderListLast12(pageQuery);
    }

    @GetMapping("/getHistoryOrderList")
    public TableDataInfo<GbOrderListVO> getHistoryOrderList(PageQuery pageQuery) {
        return gbOrderService.getHistoryOrderList(pageQuery);
    }

    @PostMapping("/createOrder")
    public R<CreateOrderVO> createOrder(@RequestBody CreateOrderDTO dto) {
        return R.ok(gbOrderService.createOrder(dto));
    }

    @PostMapping("/verifyCode")
    public R<Void> verifyCode(@RequestBody VerifyCodeDTO dto) {
        return gbOrderService.verifyCode(dto) ? R.ok() : R.fail();
    }

    @GetMapping("/getRegion")
    public R<List<GetRegionVO>> getRegion(GetRegionDTO dto) {
        return R.ok(gbOrderService.getRegion(dto));
    }

    @GetMapping("/queryOrderStatus")
    public TyVisualResp<QueryOrderStatusResp> queryOrderStatus(GbOrderEntity gbOrderEntity) {
        QueryOrderStatusReq queryOrderStatusReq = new QueryOrderStatusReq();
        queryOrderStatusReq.setOrderId(gbOrderEntity.getOrderId());
        return tySendRequestUtil.sendRequest(tyVisualConfig, TyVisualReqEnum.QUERY_ORDER_STATUS,
                JSON.toJSONString(queryOrderStatusReq), new TypeReference<>() {
                });
    }
}