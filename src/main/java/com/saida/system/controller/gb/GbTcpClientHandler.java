package com.saida.system.controller.gb;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Arrays;

@Slf4j
public class GbTcpClientHandler implements Runnable {
    private final Socket socket;

    public GbTcpClientHandler(Socket socket) {
        this.socket = socket;
    }

    @Override
    public void run() {
        try (InputStream in = socket.getInputStream();
             OutputStream out = socket.getOutputStream()) {

            while (true) {
                // 按照协议格式读取: bodyLen(4B) | type(2B) | subtype(2B) | body

                // 1. 读取消息头 (8字节: 4字节bodyLen + 2字节type + 2字节subtype)
                byte[] header = new byte[8];
                int headerBytesRead = readExactly(in, header, 8);
                if (headerBytesRead == -1) {
                    log.info("客户端断开连接");
                    break;
                }

                // 2. 解析消息头
                int bodyLen = bytesToInt(header, 0);  // 前4字节为body长度
                short type = bytesToShort(header, 4); // 第5-6字节为type
                short subtype = bytesToShort(header, 6); // 第7-8字节为subtype

                log.info("收到消息头 - bodyLen: {}, type: {}, subtype: {}", bodyLen, type, subtype);

                // 3. 读取消息体
                byte[] body = new byte[bodyLen];
                if (bodyLen > 0) {
                    int bodyBytesRead = readExactly(in, body, bodyLen);
                    if (bodyBytesRead == -1) {
                        log.error("读取消息体时连接断开");
                        break;
                    }
                }

                log.info("收到完整消息 - type: {}, subtype: {}, body: {}",
                    type, subtype, Arrays.toString(body));

                // 4. 处理消息
                handleMessage(type, subtype, body, out);
            }
        } catch (IOException e) {
            log.error("客户端异常: {}", e.getMessage(), e);
        } finally {
            try {
                socket.close();
            } catch (IOException ignored) {
            }
        }
    }

    /**
     * 精确读取指定字节数
     * @param in 输入流
     * @param buffer 缓冲区
     * @param length 要读取的字节数
     * @return 实际读取的字节数，-1表示流结束
     */
    private int readExactly(InputStream in, byte[] buffer, int length) throws IOException {
        int totalRead = 0;
        while (totalRead < length) {
            int bytesRead = in.read(buffer, totalRead, length - totalRead);
            if (bytesRead == -1) {
                return -1; // 流结束
            }
            totalRead += bytesRead;
        }
        return totalRead;
    }

    /**
     * 将4字节转换为int (小端序)
     */
    private int bytesToInt(byte[] bytes, int offset) {
        return (bytes[offset] & 0xFF) |
               ((bytes[offset + 1] & 0xFF) << 8) |
               ((bytes[offset + 2] & 0xFF) << 16) |
               ((bytes[offset + 3] & 0xFF) << 24);
    }

    /**
     * 将2字节转换为short (小端序)
     */
    private short bytesToShort(byte[] bytes, int offset) {
        return (short) ((bytes[offset] & 0xFF) |
                       ((bytes[offset + 1] & 0xFF) << 8));
    }

    /**
     * 处理接收到的消息
     */
    private void handleMessage(short type, short subtype, byte[] body, OutputStream out) throws IOException {
        log.info("处理消息 - type: {}, subtype: {}, bodyLength: {}", type, subtype, body.length);

        // 根据type和subtype处理不同类型的消息
        switch (type) {
            case 1:
                handleType1Message(subtype, body, out);
                break;
            case 2:
                handleType2Message(subtype, body, out);
                break;
            default:
                log.warn("未知消息类型: {}", type);
                // 发送错误响应
                sendErrorResponse(out, type, subtype, "Unknown message type");
        }
    }

    /**
     * 处理类型1的消息
     */
    private void handleType1Message(short subtype, byte[] body, OutputStream out) throws IOException {
        switch (subtype) {
            case 1:
                log.info("处理心跳消息");
                sendHeartbeatResponse(out);
                break;
            case 2:
                log.info("处理登录消息: {}", new String(body));
                sendLoginResponse(out, true);
                break;
            default:
                log.warn("未知子类型: {}", subtype);
        }
    }

    /**
     * 处理类型2的消息
     */
    private void handleType2Message(short subtype, byte[] body, OutputStream out) throws IOException {
        switch (subtype) {
            case 1:
                log.info("处理数据上报消息");
                sendDataResponse(out);
                break;
            default:
                log.warn("未知子类型: {}", subtype);
        }
    }

    /**
     * 发送心跳响应
     */
    private void sendHeartbeatResponse(OutputStream out) throws IOException {
        byte[] response = "HEARTBEAT_OK".getBytes();
        sendResponse(out, (short) 1, (short) 1, response);
    }

    /**
     * 发送登录响应
     */
    private void sendLoginResponse(OutputStream out, boolean success) throws IOException {
        String message = success ? "LOGIN_SUCCESS" : "LOGIN_FAILED";
        byte[] response = message.getBytes();
        sendResponse(out, (short) 1, (short) 2, response);
    }

    /**
     * 发送数据响应
     */
    private void sendDataResponse(OutputStream out) throws IOException {
        byte[] response = "DATA_RECEIVED".getBytes();
        sendResponse(out, (short) 2, (short) 1, response);
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(OutputStream out, short originalType, short originalSubtype, String error) throws IOException {
        byte[] response = error.getBytes();
        sendResponse(out, (short) 999, (short) 999, response);
    }

    /**
     * 按协议格式发送响应
     */
    private void sendResponse(OutputStream out, short type, short subtype, byte[] body) throws IOException {
        // 构造响应消息: bodyLen(4B) | type(2B) | subtype(2B) | body
        byte[] header = new byte[8];

        // bodyLen (4字节，小端序)
        intToBytes(body.length, header, 0);

        // type (2字节，小端序)
        shortToBytes(type, header, 4);

        // subtype (2字节，小端序)
        shortToBytes(subtype, header, 6);

        // 发送消息头
        out.write(header);

        // 发送消息体
        if (body.length > 0) {
            out.write(body);
        }

        out.flush();

        log.info("发送响应 - type: {}, subtype: {}, bodyLength: {}", type, subtype, body.length);
    }

    /**
     * 将int转换为4字节 (小端序)
     */
    private void intToBytes(int value, byte[] bytes, int offset) {
        bytes[offset] = (byte) (value & 0xFF);
        bytes[offset + 1] = (byte) ((value >> 8) & 0xFF);
        bytes[offset + 2] = (byte) ((value >> 16) & 0xFF);
        bytes[offset + 3] = (byte) ((value >> 24) & 0xFF);
    }

    /**
     * 将short转换为2字节 (小端序)
     */
    private void shortToBytes(short value, byte[] bytes, int offset) {
        bytes[offset] = (byte) (value & 0xFF);
        bytes[offset + 1] = (byte) ((value >> 8) & 0xFF);
    }
}
