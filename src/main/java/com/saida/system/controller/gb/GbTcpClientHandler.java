package com.saida.system.controller.gb;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Arrays;

@Slf4j
public class GbTcpClientHandler implements Runnable {
    private final Socket socket;

    public GbTcpClientHandler(Socket socket) {
        this.socket = socket;
    }

    @Override
    public void run() {
        try (InputStream in = socket.getInputStream();
             OutputStream out = socket.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                byte[] received = Arrays.copyOf(buffer, len);
                log.info("收到字节：{}", Arrays.toString(received));

                // 回显
                out.write(received);
                out.flush();
            }
        } catch (IOException e) {
            log.error("客户端异常: {}", e.getMessage(), e);
        } finally {
            try {
                socket.close();
            } catch (IOException ignored) {
            }
        }
    }
}
