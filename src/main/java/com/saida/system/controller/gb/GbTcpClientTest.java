package com.saida.system.controller.gb;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Scanner;

/**
 * GB TCP客户端测试类
 * 用于测试TCP服务器的协议解析功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class GbTcpClientTest {
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8888;
    
    public static void main(String[] args) {
        GbTcpClientTest client = new GbTcpClientTest();
        client.start();
    }
    
    public void start() {
        try (Socket socket = new Socket(SERVER_HOST, SERVER_PORT);
             InputStream in = socket.getInputStream();
             OutputStream out = socket.getOutputStream()) {
            
            log.info("连接到服务器: {}:{}", SERVER_HOST, SERVER_PORT);
            
            // 启动接收响应的线程
            Thread responseThread = new Thread(() -> receiveResponses(in));
            responseThread.setDaemon(true);
            responseThread.start();
            
            // 主线程发送测试消息
            sendTestMessages(out);
            
            // 等待用户输入
            waitForUserInput(out);
            
        } catch (IOException e) {
            log.error("客户端连接异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送测试消息
     */
    private void sendTestMessages(OutputStream out) throws IOException {
        log.info("开始发送测试消息...");
        
        // 1. 发送心跳消息
        sendHeartbeat(out);
        sleep(1000);
        
        // 2. 发送登录消息
        sendLogin(out, "testUser", "password123");
        sleep(1000);
        
        // 3. 发送数据上报消息
        sendDataReport(out, "sensor_data_12345");
        sleep(1000);
        
        // 4. 发送未知类型消息（测试错误处理）
        sendUnknownMessage(out);
        sleep(1000);
        
        log.info("测试消息发送完成");
    }
    
    /**
     * 发送心跳消息
     */
    private void sendHeartbeat(OutputStream out) throws IOException {
        log.info("发送心跳消息");
        byte[] body = "PING".getBytes();
        sendMessage(out, (short) 1, (short) 1, body);
    }
    
    /**
     * 发送登录消息
     */
    private void sendLogin(OutputStream out, String username, String password) throws IOException {
        log.info("发送登录消息: {}", username);
        String loginData = String.format("{\"username\":\"%s\",\"password\":\"%s\"}", username, password);
        byte[] body = loginData.getBytes();
        sendMessage(out, (short) 1, (short) 2, body);
    }
    
    /**
     * 发送数据上报消息
     */
    private void sendDataReport(OutputStream out, String data) throws IOException {
        log.info("发送数据上报消息: {}", data);
        byte[] body = data.getBytes();
        sendMessage(out, (short) 2, (short) 1, body);
    }
    
    /**
     * 发送未知类型消息
     */
    private void sendUnknownMessage(OutputStream out) throws IOException {
        log.info("发送未知类型消息");
        byte[] body = "unknown_message".getBytes();
        sendMessage(out, (short) 999, (short) 888, body);
    }
    
    /**
     * 按协议格式发送消息: bodyLen(4B) | type(2B) | subtype(2B) | body
     */
    private void sendMessage(OutputStream out, short type, short subtype, byte[] body) throws IOException {
        // 构造消息头 (8字节)
        byte[] header = new byte[8];
        
        // bodyLen (4字节，小端序)
        intToBytes(body.length, header, 0);
        
        // type (2字节，小端序)
        shortToBytes(type, header, 4);
        
        // subtype (2字节，小端序)
        shortToBytes(subtype, header, 6);
        
        // 发送消息头
        out.write(header);
        
        // 发送消息体
        if (body.length > 0) {
            out.write(body);
        }
        
        out.flush();
        
        log.info("已发送消息 - type: {}, subtype: {}, bodyLength: {}, body: {}", 
            type, subtype, body.length, new String(body));
    }
    
    /**
     * 接收服务器响应
     */
    private void receiveResponses(InputStream in) {
        try {
            while (true) {
                // 读取响应消息头 (8字节)
                byte[] header = new byte[8];
                int headerBytesRead = readExactly(in, header, 8);
                if (headerBytesRead == -1) {
                    log.info("服务器断开连接");
                    break;
                }
                
                // 解析响应消息头
                int bodyLen = bytesToInt(header, 0);
                short type = bytesToShort(header, 4);
                short subtype = bytesToShort(header, 6);
                
                // 读取响应消息体
                byte[] body = new byte[bodyLen];
                if (bodyLen > 0) {
                    int bodyBytesRead = readExactly(in, body, bodyLen);
                    if (bodyBytesRead == -1) {
                        log.error("读取响应消息体时连接断开");
                        break;
                    }
                }
                
                log.info("收到服务器响应 - type: {}, subtype: {}, body: {}", 
                    type, subtype, new String(body));
            }
        } catch (IOException e) {
            log.error("接收响应异常: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 等待用户输入，支持手动发送消息
     */
    private void waitForUserInput(OutputStream out) {
        Scanner scanner = new Scanner(System.in);
        log.info("输入命令 (heartbeat/login/data/quit):");
        
        while (true) {
            String input = scanner.nextLine().trim();
            
            try {
                switch (input.toLowerCase()) {
                    case "heartbeat":
                        sendHeartbeat(out);
                        break;
                    case "login":
                        sendLogin(out, "manualUser", "manualPass");
                        break;
                    case "data":
                        sendDataReport(out, "manual_data_" + System.currentTimeMillis());
                        break;
                    case "quit":
                        log.info("退出客户端");
                        return;
                    default:
                        log.info("未知命令，支持的命令: heartbeat, login, data, quit");
                }
            } catch (IOException e) {
                log.error("发送消息失败: {}", e.getMessage(), e);
                break;
            }
        }
    }
    
    /**
     * 精确读取指定字节数
     */
    private int readExactly(InputStream in, byte[] buffer, int length) throws IOException {
        int totalRead = 0;
        while (totalRead < length) {
            int bytesRead = in.read(buffer, totalRead, length - totalRead);
            if (bytesRead == -1) {
                return -1;
            }
            totalRead += bytesRead;
        }
        return totalRead;
    }
    
    /**
     * 将4字节转换为int (小端序)
     */
    private int bytesToInt(byte[] bytes, int offset) {
        return (bytes[offset] & 0xFF) |
               ((bytes[offset + 1] & 0xFF) << 8) |
               ((bytes[offset + 2] & 0xFF) << 16) |
               ((bytes[offset + 3] & 0xFF) << 24);
    }
    
    /**
     * 将2字节转换为short (小端序)
     */
    private short bytesToShort(byte[] bytes, int offset) {
        return (short) ((bytes[offset] & 0xFF) |
                       ((bytes[offset + 1] & 0xFF) << 8));
    }
    
    /**
     * 将int转换为4字节 (小端序)
     */
    private void intToBytes(int value, byte[] bytes, int offset) {
        bytes[offset] = (byte) (value & 0xFF);
        bytes[offset + 1] = (byte) ((value >> 8) & 0xFF);
        bytes[offset + 2] = (byte) ((value >> 16) & 0xFF);
        bytes[offset + 3] = (byte) ((value >> 24) & 0xFF);
    }
    
    /**
     * 将short转换为2字节 (小端序)
     */
    private void shortToBytes(short value, byte[] bytes, int offset) {
        bytes[offset] = (byte) (value & 0xFF);
        bytes[offset + 1] = (byte) ((value >> 8) & 0xFF);
    }
    
    /**
     * 线程休眠
     */
    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
