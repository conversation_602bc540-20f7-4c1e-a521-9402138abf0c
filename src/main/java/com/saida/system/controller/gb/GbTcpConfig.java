package com.saida.system.controller.gb;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * GBTcp交互 配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "gb-tcp")
public class GbTcpConfig {

    /**
     * 暴露给设备的ip + port 一定要是ip 因为ip剩字节
     */
    private String ip;

    private Integer port;

}
