package com.saida.system.controller.gb;

import com.saida.common.redis.utils.RedisUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.time.Duration;
import java.util.Random;

/**
 * GBTcp交互主入口
 */
@Slf4j
@Service
public class GbTcpServer {


    @Resource
    public GbTcpConfig gbTcpConfig;
    private static final String DEVICE_KEY_PREFIX = "GB:TCP:DEVICE_ID:";
    private static final Random RANDOM = new Random();

    /**
     * 获取一个设备ID
     * @param gbTcpDeviceBean 要和这个设备id关联的数据
     * @return 设备ID
     */
    public int getDeviceId(GbTcpDeviceBean gbTcpDeviceBean) {
        int deviceId;
        String redisKey;

        for (int i = 0; i < 1000; i++) {
            // 生成随机 4 字节整型 (正整数)
            deviceId = RANDOM.nextInt(Integer.MAX_VALUE);
            redisKey = DEVICE_KEY_PREFIX + deviceId;

            // 判断 redis 中是否已存在
            if (!RedisUtils.hasKey(redisKey)) {
                // 不存在则写入，占位（可设置过期时间，例如 24 小时，避免泄漏）
                RedisUtils.setCacheObject(redisKey, gbTcpDeviceBean, Duration.ofHours(1));
                // 返回给前端
                return deviceId;
            }
        }
        throw new RuntimeException("设备ID生成失败");
    }

    @PostConstruct
    public void init() {
        if (gbTcpConfig.getPort() == null) {
            log.error("请配置端口号");
            return;
        }
        Thread thread = new Thread(() -> {
            start(gbTcpConfig.getPort());
        });
        thread.setName("TCP-Server-Thread");
        thread.start();
    }


    @Resource
    private ApplicationContext applicationContext;

    public void start(Integer port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            log.info("TCP 服务器启动，监听端口 {}", port);
            while (true) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    Thread thread = new Thread(new GbTcpClientHandler(clientSocket));
                    thread.setName("TCP=>"+clientSocket.getRemoteSocketAddress());
                    thread.start();
                } catch (IOException e) {
                    log.error("处理客户端接入异常", e);
                }
            }
        } catch (IOException e) {
            for (int i = 0; i < 10; i++) {
                log.error("❌❌❌TCP 服务器启动异常{}", e.getMessage());
            }
            log.error("❌❌❌TCP 服务器启动异常", e);
        } finally {
            // 如果这个断了 服务也停止把
            log.error("❌❌❌ TCP出现什么问题列 我要自毁了");
            SpringApplication.exit(applicationContext, () -> 1);
        }
    }
}
