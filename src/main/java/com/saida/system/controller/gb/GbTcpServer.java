package com.saida.system.controller.gb;

import com.saida.common.redis.utils.RedisUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.time.Duration;
import java.util.Random;

/**
 * GBTcp交互主入口
 */
@Slf4j
@Service
public class GbTcpServer {


    @Resource
    public GbTcpConfig gbTcpConfig;
    @Resource
    public RedisUtils redisUtils;
    private static final String DEVICE_KEY_PREFIX = "GB:TCP:DEVICE_ID:";
    private static final Random RANDOM = new Random();

    /**
     * 获取一个设备ID
     * @param gbTcpDeviceBean 要和这个设备id关联的数据
     * @return 设备ID
     */
    public int getDeviceId(GbTcpDeviceBean gbTcpDeviceBean) {
        int deviceId;
        String redisKey;

        for (int i = 0; i < 1000; i++) {
            // 生成随机 4 字节整型 (正整数)
            deviceId = RANDOM.nextInt(Integer.MAX_VALUE);
            redisKey = DEVICE_KEY_PREFIX + deviceId;

            // 判断 redis 中是否已存在
            if (!RedisUtils.hasKey(redisKey)) {
                // 不存在则写入，占位（可设置过期时间，例如 24 小时，避免泄漏）
                RedisUtils.setCacheObject(redisKey, gbTcpDeviceBean, Duration.ofHours(1));
                // 返回给前端
                return deviceId;
            }
        }
        throw new RuntimeException("设备ID生成失败");
    }


    @PostConstruct
    public void init() {
        if (gbTcpConfig.getPort() == null) {
            log.error("请配置端口号");
            return;
        }
        start(gbTcpConfig.getPort());
    }


    public void start(Integer port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            log.info("TCP 服务器启动，监听端口 {}", port);
            while (true) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    log.info("客户端接入：{}", clientSocket.getRemoteSocketAddress());
                    new Thread(new GbTcpClientHandler(clientSocket)).start();
                } catch (IOException e) {
                    log.error("处理客户端接入异常", e);
                }
            }
        } catch (IOException e) {
            log.error("TCP 服务器启动异常", e);
        }
    }
}
