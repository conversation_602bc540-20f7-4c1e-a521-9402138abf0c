package com.saida.system.controller.gb;

import lombok.extern.slf4j.Slf4j;

/**
 * GB TCP测试运行器
 * 用于快速启动服务器和客户端进行测试
 * 
 * <AUTHOR>
 */
@Slf4j
public class GbTcpTestRunner {
    
    public static void main(String[] args) {
        if (args.length == 0) {
            printUsage();
            return;
        }
        
        String mode = args[0].toLowerCase();
        
        switch (mode) {
            case "server":
                startServer();
                break;
            case "client":
                startClient();
                break;
            case "both":
                startBoth();
                break;
            default:
                printUsage();
        }
    }
    
    /**
     * 启动服务器
     */
    private static void startServer() {
        log.info("启动TCP服务器...");
        GbTcpServer server = new GbTcpServer();
        server.start(122);
    }
    
    /**
     * 启动客户端
     */
    private static void startClient() {
        log.info("启动TCP客户端...");
        // 等待一下确保服务器已启动
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        GbTcpClientTest client = new GbTcpClientTest();
        client.start();
    }
    
    /**
     * 同时启动服务器和客户端
     */
    private static void startBoth() {
        log.info("同时启动服务器和客户端...");
        
        // 在新线程中启动服务器
        Thread serverThread = new Thread(() -> {
            GbTcpServer server = new GbTcpServer();
            server.start(123);
        });
        serverThread.setDaemon(true);
        serverThread.start();
        
        // 等待服务器启动
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 启动客户端
        GbTcpClientTest client = new GbTcpClientTest();
        client.start();
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("GB TCP测试工具使用说明:");
        System.out.println("java GbTcpTestRunner <mode>");
        System.out.println("");
        System.out.println("模式选项:");
        System.out.println("  server  - 只启动TCP服务器");
        System.out.println("  client  - 只启动TCP客户端");
        System.out.println("  both    - 同时启动服务器和客户端");
        System.out.println("");
        System.out.println("示例:");
        System.out.println("  java GbTcpTestRunner server");
        System.out.println("  java GbTcpTestRunner client");
        System.out.println("  java GbTcpTestRunner both");
    }
}
