package com.saida.system.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.saida.common.core.constant.GlobalConstants;
import com.saida.common.core.core.domain.AjaxResult;
import com.saida.common.core.core.domain.R;
import com.saida.common.excel.utils.ExcelUtil;
import com.saida.common.log.annotation.Log;
import com.saida.common.log.enums.BusinessType;
import com.saida.common.orm.core.page.TableDataInfo;
import com.saida.common.redis.utils.RedisUtils;
import com.saida.common.web.core.BaseController;
import com.saida.system.domain.bo.SysLogininforBo;
import com.saida.system.domain.vo.SysLogininforVo;
import com.saida.system.service.ISysLogininforService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController
{
    @Resource
    private ISysLogininforService logininforService;

    @SaCheckPermission("monitor:logininfor:list")
    @GetMapping("/list")
    public TableDataInfo<SysLogininforVo>  list(SysLogininforBo logininforBo)
    {
        return logininforService.selectPage(logininforBo);
    }

    /**
     * 导出系统访问记录列表
     */
    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @SaCheckPermission("monitor:logininfor:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLogininforBo logininfor)
    {
        List<SysLogininforVo> list = logininforService.selectLogininforList(logininfor);
        ExcelUtil.exportExcel(list, "登录日志", SysLogininforVo.class, response);
    }

    /**
     * 批量删除登录日志
     * @param infoIds 日志ids
     */
    @SaCheckPermission("monitor:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public R<Void> remove(@PathVariable Long[] infoIds)
    {
        boolean deleted = logininforService.deleteLogininforByIds(infoIds);
        if (!deleted) {
            R.fail("删除登录日志记录失败!");
        }
        return R.ok();
    }

    /**
     * 清理系统访问记录
     */
    @SaCheckPermission("monitor:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public R<Void> clean()
    {
        boolean cleaned = logininforService.cleanLogininfor();
        if (!cleaned) {
            R.fail("清除登录日志记录失败!");
        }
        return R.ok();
    }

    @SaCheckPermission("monitor:logininfor:unlock")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public AjaxResult unlock(@PathVariable("userName") String userName)
    {
        String loginName = GlobalConstants.PWD_ERR_CNT_KEY + userName;
        if (RedisUtils.hasKey(loginName)) {
            RedisUtils.deleteObject(loginName);
        }
        return success();
    }
}
