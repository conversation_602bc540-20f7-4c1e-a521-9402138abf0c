package com.saida.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.saida.common.core.core.domain.R;
import com.saida.common.excel.utils.ExcelUtil;
import com.saida.common.log.annotation.Log;
import com.saida.common.log.enums.BusinessType;
import com.saida.common.orm.core.page.TableDataInfo;
import com.saida.common.web.annotation.RepeatSubmit;
import com.saida.system.domain.bo.SysClientBo;
import com.saida.system.domain.vo.SysClientVo;
import com.saida.system.service.ISysClientService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统授权表 控制层。
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/client")
public class SysClientController {

    @Resource
    private ISysClientService sysClientService;

    /**
     * 查询客户端管理列表
     */
    @SaCheckPermission("system:client:list")
    @GetMapping("/list")
    public TableDataInfo<SysClientVo> list(SysClientBo sysClientBo) {
        return sysClientService.selectPage(sysClientBo);
    }

    /**
     * 导出客户端管理列表
     */
    @SaCheckPermission("system:client:export")
    @Log(title = "客户端管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysClientBo sysClientBo, HttpServletResponse response) {
        List<SysClientVo> list = sysClientService.selectList(sysClientBo);
        ExcelUtil.exportExcel(list, "客户端管理", SysClientVo.class, response);
    }

    /**
     * 获取客户端管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:client:query")
    @GetMapping("/{id}")
    public R<SysClientVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(sysClientService.selectById(id));
    }

    /**
     * 新增客户端管理
     */
    @SaCheckPermission("system:client:add")
    @Log(title = "客户端管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody SysClientBo sysClientBo) {
        boolean inserted = sysClientService.insert(sysClientBo);
        if (!inserted) {
            return R.fail("新增客户端管理记录失败！");
        }
        return R.ok();
    }

    /**
     * 修改客户端管理
     */
    @SaCheckPermission("system:client:edit")
    @Log(title = "客户端管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody SysClientBo sysClientBo) {
        boolean updated = sysClientService.update(sysClientBo);
        if (!updated) {
            return R.fail("修改客户端管理记录失败!");
        }
        return R.ok();
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:client:edit")
    @Log(title = "客户端管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysClientBo sysClientBo) {
        boolean updated = sysClientService.updateStatus(sysClientBo);
        if (!updated) {
            return R.fail("修改客户端管理状态失败!");
        }
        return R.ok();
    }

    /**
     * 删除客户端管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:client:remove")
    @Log(title = "客户端管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        boolean deleted = sysClientService.deleteByIds(List.of(ids));
        if (!deleted) {
            return R.fail("删除客户端管理记录失败!");
        }
        return R.ok();
    }
}
