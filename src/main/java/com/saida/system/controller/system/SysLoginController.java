package com.saida.system.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import com.saida.common.core.constant.Constants;
import com.saida.common.core.domain.R;
import com.saida.common.core.domain.bo.*;
import com.saida.common.satoken.utils.LoginHelper;
import com.saida.system.domain.entity.SysMenu;
import com.saida.system.domain.vo.RouterVo;
import com.saida.system.domain.vo.SysUserVo;
import com.saida.system.service.SysLoginService;
import com.saida.system.service.SysMenuService;
import com.saida.system.service.SysUserService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    private final SysLoginService loginService;
    private final SysMenuService menuService;
    private final SysUserService userService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        return R.ok(Map.of(Constants.TOKEN, token));
    }

    /**
     * 短信登录
     *
     * @param smsLoginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/smsLogin")
    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        // 生成令牌
        String token = loginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        return R.ok(Map.of(Constants.TOKEN, token));
    }

    /**
     * 邮件登录
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/emailLogin")
    public R<Map<String, Object>> emailLogin(@Validated @RequestBody EmailLoginBody body) {
        // 生成令牌
        String token = loginService.emailLogin(body.getEmail(), body.getEmailCode());
        return R.ok(Map.of(Constants.TOKEN, token));
    }

    /**
     * 小程序登录
     *
     * @param xcxLoginBody
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/xcxLogin")
    public R<Map<String, Object>> xcxLogin(@RequestBody XcxLoginBody xcxLoginBody) {
        String token = loginService.xcxLogin(xcxLoginBody);
        return R.ok(Map.of(Constants.TOKEN, token));
    }

    /**
     * 退出登录
     */
    @SaIgnore
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUserVo user = userService.selectUserById(loginUser.getUserId());
        if (user == null) {
            return R.fail("用户信息不存在");
        }

        return R.ok(Map.of(
                        "user", user,
                        "roles", loginUser.getRolePermission() != null ? loginUser.getRolePermission() : Collections.emptyList(),
                        "permissions", loginUser.getMenuPermission() != null ? loginUser.getMenuPermission() : Collections.emptyList()
                )
        );
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(LoginHelper.getUserId());
        List<RouterVo> routerVos = menuService.buildMenus(menus);
        return R.ok(menuService.resetChildrenName(routerVos));
    }
}
