package com.saida.system.controller.system;

import com.saida.common.core.core.domain.R;
import com.saida.common.security.utils.LoginHelper;
import com.saida.common.web.core.BaseController;
import com.saida.system.domain.vo.SysSocialVo;
import com.saida.system.service.ISysSocialService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 社会化关系
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/social")
public class SysSocialController extends BaseController {

    @Resource
    private ISysSocialService sysSocialService;

    /**
     * 查询社会化关系列表
     */
    @GetMapping("/list")
    public R<List<SysSocialVo>> list() {
        return R.ok(sysSocialService.selectListByUserId(LoginHelper.getUserId()));
    }

}
