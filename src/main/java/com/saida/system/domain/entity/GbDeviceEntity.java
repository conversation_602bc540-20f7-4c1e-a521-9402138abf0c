package com.saida.system.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gb_device")
public class GbDeviceEntity extends BaseEntity {

    @TableId
    private Long id;

    private Long orderId;

    private String sn;

    private String model;

    private String name;

    private String longitude;

    private String latitude;

    private String remark;
}