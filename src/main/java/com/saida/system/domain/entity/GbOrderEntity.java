package com.saida.system.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gb_order")
public class GbOrderEntity extends BaseEntity {

    @TableId
    private Long id;

    private Long orderId;

    private Long userId;

    private String phone;

    private Integer status;

    private Integer verifyCode;
}