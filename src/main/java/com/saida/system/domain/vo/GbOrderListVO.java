package com.saida.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.saida.common.sensitive.annotation.Sensitive;
import com.saida.common.sensitive.core.SensitiveStrategy;
import com.saida.common.translation.annotation.Translation;
import com.saida.common.translation.constant.TransConstant;
import com.saida.system.domain.entity.SysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@AutoMapper(target = SysUser.class)
public class GbOrderListVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long orderId;

    private Long userId;

    private String phone;

    private Integer status;

    private String createTime;

    private String sn;

    private String model;

    private String name;

    private String longitude;

    private String latitude;
}