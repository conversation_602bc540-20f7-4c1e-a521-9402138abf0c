package com.saida.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.system.domain.dto.GbOrderListDTO;
import com.saida.system.domain.entity.GbOrderEntity;
import com.saida.system.domain.vo.GbOrderListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GbOrderMapper extends BaseMapper<GbOrderEntity> {

    List<GbOrderListVO> getGbOrderList(@Param("dto") GbOrderListDTO dto);
}