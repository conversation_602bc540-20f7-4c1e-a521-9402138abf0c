package com.saida.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.common.mybatis.core.page.PageQuery;
import com.saida.common.mybatis.core.page.TableDataInfo;
import com.saida.system.domain.dto.CreateOrderDTO;
import com.saida.system.domain.dto.GetRegionDTO;
import com.saida.system.domain.dto.VerifyCodeDTO;
import com.saida.system.domain.entity.GbOrderEntity;
import com.saida.system.domain.vo.CreateOrderVO;
import com.saida.system.domain.vo.GbOrderListVO;
import com.saida.system.domain.vo.GetRegionVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface GbOrderService extends IService<GbOrderEntity> {

    TableDataInfo<GbOrderListVO> getOrderListLast12(PageQuery pageQuery);

    TableDataInfo<GbOrderListVO> getHistoryOrderList(PageQuery pageQuery);

    CreateOrderVO createOrder(CreateOrderDTO dto);

    boolean verifyCode(VerifyCodeDTO dto);

    List<GetRegionVO> getRegion(@RequestBody GetRegionDTO dto);
}