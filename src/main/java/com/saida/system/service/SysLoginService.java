package com.saida.system.service;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.saida.common.core.constant.CacheConstants;
import com.saida.common.core.constant.Constants;
import com.saida.common.core.domain.bo.LoginUser;
import com.saida.common.core.domain.bo.XcxLoginBody;
import com.saida.common.core.domain.bo.XcxLoginUser;
import com.saida.common.core.domain.vo.RoleVO;
import com.saida.common.core.enums.DeviceType;
import com.saida.common.core.enums.LoginType;
import com.saida.common.core.enums.UserStatus;
import com.saida.common.core.enums.UserType;
import com.saida.common.core.exception.user.CaptchaException;
import com.saida.common.core.exception.user.CaptchaExpireException;
import com.saida.common.core.exception.user.UserException;
import com.saida.common.core.utils.*;
import com.saida.common.log.event.LogininforEvent;
import com.saida.common.redis.utils.RedisUtils;
import com.saida.common.satoken.utils.LoginHelper;
import com.saida.common.web.config.properties.CaptchaProperties;
import com.saida.system.config.WeChatMiniProgramConfig;
import com.saida.system.domain.entity.SysUser;
import com.saida.system.domain.vo.SysDeptVo;
import com.saida.system.domain.vo.SysRoleVo;
import com.saida.system.domain.vo.SysUserVo;
import com.saida.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysLoginService {

    private final SysUserMapper userMapper;
    private final CaptchaProperties captchaProperties;
    private final SysPermissionService permissionService;
    private final SysRoleService roleService;
    private final SysDeptService deptService;
    private final WeChatMiniProgramConfig weChatMiniProgramConfig;

    @Value("${user.password.maxRetryCount}")
    private Integer maxRetryCount;

    @Value("${user.password.lockTime}")
    private Integer lockTime;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        boolean captchaEnabled = captchaProperties.getEnable();
        // 验证码开关
        if (captchaEnabled) {
            validateCaptcha(username, code, uuid);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        SysUserVo user = loadUserByUsername(username);
        checkLogin(LoginType.PASSWORD, username, () -> !BCrypt.checkpw(password, user.getPassword()));
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = buildLoginUser(user);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.PC);

        recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), username);
        return StpUtil.getTokenValue();
    }

    public String smsLogin(String phonenumber, String smsCode) {
        // 通过手机号查找用户
        SysUserVo user = loadUserByPhonenumber(phonenumber);

        checkLogin(LoginType.SMS, user.getUserName(), () -> !validateSmsCode(phonenumber, smsCode));
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = buildLoginUser(user);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.APP);

        recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), user.getUserName());
        return StpUtil.getTokenValue();
    }

    public String emailLogin(String email, String emailCode) {
        // 通过手邮箱查找用户
        SysUserVo user = loadUserByEmail(email);

        checkLogin(LoginType.EMAIL, user.getUserName(), () -> !validateEmailCode(email, emailCode));
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = buildLoginUser(user);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.APP);

        recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), user.getUserName());
        return StpUtil.getTokenValue();
    }

    public String xcxLogin(XcxLoginBody xcxLoginBody) {
        String code = xcxLoginBody.getCode();
        String encryptedData = xcxLoginBody.getEncryptedData();
        String iv = xcxLoginBody.getIv();
        if (StringUtils.isBlank(code) || StringUtils.isBlank(encryptedData) || StringUtils.isBlank(iv)) {
            throw new RuntimeException("参数错误");
        }

        // 1. 调用微信接口获取openid和session_key
        String url = String.format(weChatMiniProgramConfig.getLoginUrl(), weChatMiniProgramConfig.getAppid(), weChatMiniProgramConfig.getSecret(), code);

        try {
            String resp = HttpUtil.get(url);
            JSONObject result = JSONUtil.parseObj(resp);

            if (result.containsKey("errcode")) {
                throw new RuntimeException("微信登录失败: " + result.getStr("errmsg"));
            }

            String openId = result.getStr("openid");
            String sessionKey = result.getStr("session_key");

            // 3. 解密手机号（核心步骤）
            String phoneNumber = decryptPhoneNumber(encryptedData, iv, sessionKey);

            // 2. 检查用户是否已存在
            SysUserVo sysUserVo = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getOpenId, openId)
            );

            Long userId;
            LocalDateTime localDateTime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            if (Objects.isNull(sysUserVo)) {
                SysUser sysUser = new SysUser();
                userId = IdUtil.getSnowflakeNextId();
                sysUser.setUserId(userId);
                sysUser.setUserName(phoneNumber);
                sysUser.setPhonenumber(phoneNumber);
                sysUser.setNickName(this.getNickNameByPhone(phoneNumber));
                sysUser.setUserType(UserType.APP_USER.getUserType());
                sysUser.setStatus("1");
                sysUser.setLoginDate(new Date());
                sysUser.setOpenId(openId);
                sysUser.setSessionKey(sessionKey);
                sysUser.setRemark("小程序用户");
                sysUser.setCreateBy(phoneNumber);
                sysUser.setCreateTime(localDateTime);
                userMapper.insert(sysUser);
            } else {
                userId = sysUserVo.getUserId();
                LambdaUpdateWrapper<SysUser> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.set(!Objects.equals(sysUserVo.getUserName(), phoneNumber), SysUser::getUserName, phoneNumber);
                lambdaUpdateWrapper.set(!Objects.equals(sysUserVo.getPhonenumber(), phoneNumber), SysUser::getPhonenumber, phoneNumber);
                lambdaUpdateWrapper.set(SysUser::getSessionKey, sessionKey);
                lambdaUpdateWrapper.set(SysUser::getLoginDate, new Date());
                lambdaUpdateWrapper.set(SysUser::getUpdateBy, sysUserVo.getPhonenumber());
                lambdaUpdateWrapper.set(SysUser::getUpdateTime, localDateTime);
                lambdaUpdateWrapper.eq(SysUser::getUserId, sysUserVo.getUserId());
                userMapper.update(lambdaUpdateWrapper);
            }

            XcxLoginUser loginUser = new XcxLoginUser();
            loginUser.setUserId(userId);
            loginUser.setUsername(phoneNumber);
            loginUser.setUserType(UserType.APP_USER.getUserType());
            loginUser.setOpenid(openId);
            // 生成token
            LoginHelper.loginByDevice(loginUser, DeviceType.XCX);

            recordLogininfor(phoneNumber, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
            recordLoginInfo(userId, phoneNumber);
            return StpUtil.getTokenValue();
        } catch (Exception e) {
            log.error("调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信登录失败");
        }
    }

    /**
     * 退出登录
     */
    public void logout() {
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            recordLogininfor(loginUser.getUsername(), Constants.LOGOUT, MessageUtils.message("user.logout.success"));
        } catch (NotLoginException ignored) {
        } finally {
            try {
                StpUtil.logout();
            } catch (NotLoginException ignored) {
            }
        }
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     */
    private void recordLogininfor(String username, String status, String message) {
        LogininforEvent logininforEvent = new LogininforEvent();
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(status);
        logininforEvent.setMessage(message);
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

    /**
     * 校验短信验证码
     */
    private boolean validateSmsCode(String phonenumber, String smsCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        return code.equals(smsCode);
    }

    /**
     * 校验邮箱验证码
     */
    private boolean validateEmailCode(String email, String emailCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + email);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(email, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        return code.equals(emailCode);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.defaultString(uuid, "");
        String captcha = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        if (captcha == null) {
            recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
            throw new CaptchaException();
        }
    }

    private SysUserVo loadUserByUsername(String username) {
        SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UserException("user.not.exists", username);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new UserException("user.blocked", username);
        }
        return user;
    }

    private SysUserVo loadUserByPhonenumber(String phonenumber) {
        SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhonenumber, phonenumber));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", phonenumber);
            throw new UserException("user.not.exists", phonenumber);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", phonenumber);
            throw new UserException("user.blocked", phonenumber);
        }
        return user;
    }

    private SysUserVo loadUserByEmail(String email) {
        SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, email));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", email);
            throw new UserException("user.not.exists", email);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", email);
            throw new UserException("user.blocked", email);
        }
        return user;
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(SysUserVo user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(user.getUserId());
        loginUser.setDeptId(user.getDeptId());
        loginUser.setUsername(user.getUserName());
        loginUser.setUserType(user.getUserType());
        loginUser.setMenuPermission(permissionService.getMenuPermission(user.getUserId()));
        loginUser.setRolePermission(permissionService.getRolePermission(user.getUserId()));

        SysDeptVo dept = null;
        if (ObjectUtil.isNotNull(user.getDeptId())) {
            dept = deptService.selectDeptById(user.getDeptId());
        }
        loginUser.setDeptName(ObjectUtil.isNull(dept) ? "" : dept.getDeptName());
        List<SysRoleVo> roles = roleService.selectRolesByUserId(user.getUserId());
        loginUser.setRoles(BeanUtil.copyToList(roles, RoleVO.class));

        return loginUser;
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId, String username) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(ServletUtils.getClientIP());
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(username);
        userMapper.updateById(sysUser);
    }

    /**
     * 登录校验
     */
    private void checkLogin(LoginType loginType, String username, Supplier<Boolean> supplier) {
        String clientIP = ServletUtils.getClientIP();
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username + ":" + clientIP;
        String loginFail = Constants.LOGIN_FAIL;

        // 获取用户登录错误次数，默认为0 (可自定义限制策略 例如: key + username + ip)
        int errorNumber = ObjectUtil.defaultIfNull(RedisUtils.getCacheObject(errorKey), 0);
        // 锁定时间内登录 则踢出
        if (errorNumber >= maxRetryCount) {
            recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
            throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
        }

        if (supplier.get()) {
            // 错误次数递增
            errorNumber++;
            RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(lockTime));
            // 达到规定错误次数 则锁定登录
            if (errorNumber >= maxRetryCount) {
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
                throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
            } else {
                // 未达到规定错误次数
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitCount(), errorNumber));
                throw new UserException(loginType.getRetryLimitCount(), errorNumber);
            }
        }

        // 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
    }

    /**
     * 解密手机号核心方法（AES-128-CBC）
     */
    private String decryptPhoneNumber(String encryptedData, String iv, String sessionKey) {
        try {
            // Base64解码
            byte[] encryptedDataBytes = Base64.decode(encryptedData);
            byte[] ivBytes = Base64.decode(iv);
            byte[] sessionKeyBytes = Base64.decode(sessionKey);

            // 关键修改：用 PKCS5Padding 替代 PKCS7Padding
            AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, sessionKeyBytes, ivBytes);

            // 解密
            byte[] decryptedBytes = aes.decrypt(encryptedDataBytes);
            JSONObject decryptedJson = JSONUtil.parseObj(new String(decryptedBytes, "UTF-8"));

            // 校验appid
            String appid = decryptedJson.getJSONObject("watermark").getStr("appid");
            if (!appid.equals(weChatMiniProgramConfig.getAppid())) {
                throw new RuntimeException("解密失败：水印appid不匹配");
            }

            return decryptedJson.getStr("phoneNumber");
        } catch (Exception e) {
            throw new RuntimeException("手机号解密失败：" + e.getMessage());
        }
    }

    /**
     * 用手机号后四位生成默认昵称
     */
    private String getNickNameByPhone(String phone) {
        if (phone != null && phone.length() >= 4) {
            // 1. 获取手机号后四位
            String lastFour = phone.substring(phone.length() - 4);

            // 2. 生成4位随机数字和字母（包含大小写）
            String randomStr = generateRandomStr(4);

            // 3. 组合结果
            return lastFour + randomStr;
        }
        return "小程序用户";
    }

    private String generateRandomStr(int length) {
        // 字符源：数字 + 大小写字母
        String charSource = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            // 随机从字符源中选取一个字符
            int index = random.nextInt(charSource.length());
            sb.append(charSource.charAt(index));
        }
        return sb.toString();
    }
}