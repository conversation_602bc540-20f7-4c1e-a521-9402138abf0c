package com.saida.system.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.saida.common.mybatis.core.page.PageQuery;
import com.saida.common.mybatis.core.page.TableDataInfo;
import com.saida.common.satoken.utils.LoginHelper;
import com.saida.system.domain.dto.CreateOrderDTO;
import com.saida.system.domain.dto.GbOrderListDTO;
import com.saida.system.domain.dto.GetRegionDTO;
import com.saida.system.domain.dto.VerifyCodeDTO;
import com.saida.system.domain.entity.GbDeviceEntity;
import com.saida.system.domain.entity.GbOrderEntity;
import com.saida.system.domain.vo.CreateOrderVO;
import com.saida.system.domain.vo.GbOrderListVO;
import com.saida.system.domain.vo.GetRegionVO;
import com.saida.system.mapper.GbOrderMapper;
import com.saida.system.service.GbDeviceService;
import com.saida.system.service.GbOrderService;
import com.saida.system.ty.config.TyVisualConfig;
import com.saida.system.ty.enums.TyVisualReqEnum;
import com.saida.system.ty.pojo.req.CreateOrderReq;
import com.saida.system.ty.pojo.req.GetRegionReq;
import com.saida.system.ty.pojo.req.VerifyCodeReq;
import com.saida.system.ty.pojo.resp.CreateOrderResp;
import com.saida.system.ty.pojo.resp.GetRegionResp;
import com.saida.system.ty.pojo.resp.TyVisualResp;
import com.saida.system.ty.util.TySendRequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class GbOrderServiceImpl extends ServiceImpl<GbOrderMapper, GbOrderEntity> implements GbOrderService {

    private final GbDeviceService gbDeviceService;
    private final TySendRequestUtil tySendRequestUtil;
    private final TyVisualConfig tyVisualConfig;

    @Override
    public TableDataInfo<GbOrderListVO> getOrderListLast12(PageQuery pageQuery) {
        try (Page<GbOrderListVO> page = PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize())) {
            GbOrderListDTO dto = new GbOrderListDTO();
            dto.setUserId(Objects.requireNonNull(LoginHelper.getLoginUser()).getUserId());
            dto.setBegDateTime(DateUtil.format(DateUtil.offsetHour(DateUtil.date(), -12), DatePattern.NORM_DATETIME_PATTERN));
            baseMapper.getGbOrderList(dto);
            return TableDataInfo.build(page);
        }
    }

    @Override
    public TableDataInfo<GbOrderListVO> getHistoryOrderList(PageQuery pageQuery) {
        try (Page<GbOrderListVO> page = PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize())) {
            GbOrderListDTO dto = new GbOrderListDTO();
            dto.setUserId(Objects.requireNonNull(LoginHelper.getLoginUser()).getUserId());
            dto.setEndDateTime(DateUtil.format(DateUtil.offsetHour(DateUtil.date(), -12), DatePattern.NORM_DATETIME_PATTERN));
            baseMapper.getGbOrderList(dto);
            return TableDataInfo.build(page);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderVO createOrder(CreateOrderDTO dto) {
        String phone = Objects.requireNonNull(LoginHelper.getLoginUser()).getUsername();

        String deviceName = dto.getDeviceName();
        String deviceSn = dto.getDeviceSn();
        String deviceModel = dto.getDeviceModel();
        String longitude = dto.getLongitude();
        String latitude = dto.getLatitude();

        CreateOrderReq createOrderReq = new CreateOrderReq();
        createOrderReq.setConsumerPhone(phone);
        createOrderReq.setDeviceName(deviceName);
        createOrderReq.setLongitude(longitude);
        createOrderReq.setLatitude(latitude);
        createOrderReq.setSn(deviceSn);
        createOrderReq.setDeviceModel(deviceModel);
        TyVisualResp<CreateOrderResp> tyVisualResp = tySendRequestUtil.sendRequest(tyVisualConfig, TyVisualReqEnum.CREATE_ORDER,
                JSON.toJSONString(createOrderReq), new TypeReference<>() {
                });
        if (!tyVisualResp.success()) {
            throw new RuntimeException(tyVisualResp.getMsg());
        }

        CreateOrderResp createOrderResp = tyVisualResp.getData();
        // 1.新增订单信息
        Long orderId = IdUtil.getSnowflakeNextId();
        GbOrderEntity gbOrderEntity = new GbOrderEntity();
        gbOrderEntity.setId(orderId);
        gbOrderEntity.setOrderId(createOrderResp.getOrderId());
        gbOrderEntity.setUserId(LoginHelper.getUserId());
        gbOrderEntity.setPhone(phone);
        gbOrderEntity.setStatus(1);

        // 2.新增设备信息
        GbDeviceEntity gbDeviceEntity = new GbDeviceEntity();
        gbDeviceEntity.setOrderId(orderId);
        gbDeviceEntity.setSn(deviceSn);
        gbDeviceEntity.setModel(deviceModel);
        gbDeviceEntity.setName(deviceName);
        gbDeviceEntity.setLongitude(longitude);
        gbDeviceEntity.setLatitude(latitude);

        if (Objects.equals(1, createOrderResp.getNeedSmsCode())) {
            gbOrderEntity.setVerifyCode(0);
        } else {
            gbOrderEntity.setVerifyCode(1);
        }
        this.save(gbOrderEntity);
        gbDeviceService.save(gbDeviceEntity);

        CreateOrderVO createOrderVO = new CreateOrderVO();
        createOrderVO.setId(orderId);
        createOrderVO.setVerifyCode(createOrderResp.getNeedSmsCode());
        return createOrderVO;
    }

    @Override
    public boolean verifyCode(VerifyCodeDTO dto) {
        Long id = dto.getId();
        String smsCode = dto.getSmsCode();

        GbOrderEntity gbOrderEntity = this.getById(id);
        if (gbOrderEntity == null || Objects.isNull(gbOrderEntity.getOrderId())) {
            log.error("订单不存在...id={}", id);
            throw new RuntimeException("订单不存在");
        }

        VerifyCodeReq verifyCodeReq = new VerifyCodeReq();
        verifyCodeReq.setOrderId(gbOrderEntity.getOrderId());
        verifyCodeReq.setSmsCode(smsCode);
        TyVisualResp<Void> tyVisualResp = tySendRequestUtil.sendRequest(tyVisualConfig, TyVisualReqEnum.VERIFY_CODE,
                JSON.toJSONString(verifyCodeReq), new TypeReference<>() {
                });
        if (tyVisualResp.success()) {
            LambdaUpdateWrapper<GbOrderEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(GbOrderEntity::getId, id);
            lambdaUpdateWrapper.set(GbOrderEntity::getVerifyCode, 1);
            lambdaUpdateWrapper.set(GbOrderEntity::getUpdateBy, LoginHelper.getUsername());
            lambdaUpdateWrapper.set(GbOrderEntity::getUpdateTime, DateUtil.date());
            this.update(lambdaUpdateWrapper);
            return true;
        }
        return false;
    }

    @Override
    public List<GetRegionVO> getRegion(GetRegionDTO dto) {
        Long id = dto.getId();
        GbOrderEntity gbOrderEntity = this.getById(id);
        if (gbOrderEntity == null || Objects.isNull(gbOrderEntity.getOrderId())) {
            log.error("订单不存在...id={}", id);
            throw new RuntimeException("订单不存在");
        }

        GetRegionReq getRegionReq = new GetRegionReq();
        getRegionReq.setOrderId(gbOrderEntity.getOrderId());
        getRegionReq.setRegionId(dto.getRegionId());
        getRegionReq.setLevel(dto.getLevel());
        TyVisualResp<List<GetRegionResp>> tyVisualResp = tySendRequestUtil.sendRequest(tyVisualConfig, TyVisualReqEnum.VERIFY_CODE,
                JSON.toJSONString(getRegionReq), new TypeReference<>() {
                });
        if (tyVisualResp.success()) {
            List<GetRegionResp> getRegionRespList = tyVisualResp.getData();
            if(CollectionUtil.isNotEmpty(getRegionRespList)){
                return getRegionRespList.stream().map(getRegionResp -> {
                    GetRegionVO getRegionVO = new GetRegionVO();
                    getRegionVO.setRegionId(getRegionResp.getRegionId());
                    getRegionVO.setRegionName(getRegionResp.getRegionName());
                    getRegionVO.setRegionCode(getRegionResp.getRegionCode());
                    getRegionVO.setLevel(getRegionResp.getLevel());
                    getRegionVO.setHasChildren(getRegionResp.getHasChildren());
                    return getRegionVO;
                }).toList();
            }
        }
        return List.of();
    }
}