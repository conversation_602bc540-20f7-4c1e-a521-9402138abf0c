package com.saida.system.ty.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TyVisualOrderCodeEnum {

    ONE(500, "第%s次设备状态探测中"),
    TWO(2000, "工单已完成"),
    THREE(2001, "设备注册失败！"),
    FOUR(2002, "用户套餐不足"),
    FIVE(2003, "工单已经异常终止"),
    SIX(2004, "设备绑定异常"),
    SEVEN(2005, "设备挂载异常"),
    EIGHT(2006, "工单超时未处理！"),

    ;

    private final Integer code;

    private final String msg;
}