package com.saida.system.ty.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TyVisualReqEnum {

    QUERY_ORDER_STATUS("/ibe/gb-manager/order/v1/queryOrderStatus", "天翼视联-查询工单状态"),

    CREATE_ORDER("/ibe/gb-manager/order/v1/create", "天翼视联-创建装维工单"),

    VERIFY_CODE("/ibe/gb-manager/order/v1/verifyCode", "天翼视联-验证码校验"),

    GET_REGION("/ibe/gb-manager/order/v1/getRegion", "天翼视联-获取区域层级"),

    EXECUTE_ORDER("/ibe/gb-manager/order/v1/executeOrder", "天翼视联-获取国标配置信息并执行工单"),

    ;

    /*
     * 接口路径
     */
    private final String path;

    /*
     * 接口描述
     */
    private final String des;
}