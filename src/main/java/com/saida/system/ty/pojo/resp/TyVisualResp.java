package com.saida.system.ty.pojo.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.saida.system.ty.enums.TyVisualCodeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@JsonInclude
public class TyVisualResp<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String msg;

    private T data;

    public boolean success() {
        return TyVisualCodeEnum.ZERO.getCode().equals(this.code);
    }
}