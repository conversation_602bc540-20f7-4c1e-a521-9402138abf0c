package com.saida.system.ty.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.saida.system.ty.config.TyVisualConfig;
import com.saida.system.ty.enums.TyVisualReqEnum;
import com.saida.system.ty.pojo.req.TyVisualReq;
import com.saida.system.ty.pojo.resp.TyVisualResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Slf4j
@Component
public class TySendRequestUtil {
    private static final int DELTA = 0x9E3779B9;

    public synchronized <T> TyVisualResp<T> sendRequest(TyVisualConfig tyVisualConfig, TyVisualReqEnum tyVisualReqEnum, String paramJsonString, TypeReference<TyVisualResp<T>> typeReference) {

        String des = tyVisualReqEnum.getDes();
        String path = tyVisualReqEnum.getPath();

        String url = tyVisualConfig.getHost() + path;
        TyVisualResp<T> tyVisualResp = new TyVisualResp<>();
        String resp = null;
        String paramResultJsonString = "";
        try {
            // 生成参数
            TyVisualReq req = this.apiGen(paramJsonString, tyVisualConfig);
            paramResultJsonString = JSON.toJSONString(req);
            log.debug("{}..开始...url={}, req={}", des, url, paramResultJsonString);
            resp = HttpUtil.post(url, paramResultJsonString);
            log.debug("{}..结束...url={}, req={}, resp={}", des, url, paramResultJsonString, resp);
            tyVisualResp = JSON.parseObject(resp, typeReference);
        } catch (Exception e1) {
            log.error("{}..异常...url={}, req={}, resp={}, msg={}", des, url, paramResultJsonString, resp, e1.getMessage(), e1);
        }
        return tyVisualResp;
    }

    public TyVisualReq apiGen(String paramJsonString, TyVisualConfig tyVisualConfig) {
        String appId = tyVisualConfig.getAppId();
        String appSecret = tyVisualConfig.getAppSecret();
        String version = tyVisualConfig.getVersion();
        Integer clientType = tyVisualConfig.getClientType();

        try {
            // 加密参数
            String params = this.encryptXXTEA(paramJsonString, appSecret);
            // 生成时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            // 生成签名源串
            String signSrc = appId + clientType + params + timestamp + version;
            // 生成签名
            String signature = this.encodeHmacSHA256(signSrc, appSecret);

            TyVisualReq tyVisualReq = new TyVisualReq();
            tyVisualReq.setAppId(appId);
            tyVisualReq.setVersion(version);
            tyVisualReq.setClientType(clientType);
            tyVisualReq.setParams(params);
            tyVisualReq.setSignature(signature);
            tyVisualReq.setTimestamp(timestamp);
            return tyVisualReq;
        } catch (Exception e) {
            log.error("天翼视联.生成签名异常...msg={}", e.getMessage(), e);
            throw new RuntimeException("天翼视联.生成签名异常");
        }
    }

    /**
     * XXTEA加密主函数
     */
    public String encryptXXTEA(String plaintext, String hexKey) {
        if (plaintext == null || plaintext.isEmpty() || hexKey == null || hexKey.isEmpty()) {
            return null;
        }

        // 1. 明文 -> UTF8字节数组
        byte[] plainBytes = plaintext.getBytes(StandardCharsets.UTF_8);

        // 2. Hex密钥 -> 字节数组
        byte[] keyBytes = hexToBytes(hexKey);

        // 3. 转为int数组（includeLength = true）
        int[] v = toIntArray(plainBytes, true);
        int[] k = toIntArray(keyBytes, false);

        // 4. 加密
        int[] encryptedInts = xxteaEncrypt(v, k);

        // 5. 转回字节数组（includeLength = false）
        byte[] cipherBytes = toByteArray(encryptedInts, false);

        // 6. 转为Hex字符串输出
        return bytesToHex(cipherBytes);
    }

    /**
     * XXTEA加密核心算法
     */
    private int[] xxteaEncrypt(int[] v, int[] k) {
        int n = v.length - 1;
        if (n < 1) {
            return v;
        }

        // 确保密钥至少4个int
        int[] key = Arrays.copyOf(k, 4);

        int z = v[n];
        int y = v[0];
        int sum = 0;
        int e;
        int q = 6 + 52 / (n + 1);

        while (q-- > 0) {
            sum += DELTA;
            e = (sum >>> 2) & 3;
            for (int p = 0; p < n; p++) {
                y = v[p + 1];
                z = v[p] += ((z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4)) ^ ((sum ^ y) + (key[p & 3 ^ e] ^ z));
            }
            y = v[0];
            z = v[n] += ((z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4)) ^ ((sum ^ y) + (key[n & 3 ^ e] ^ z));
        }

        return v;
    }

    /**
     * 字节数组转int数组
     */
    private int[] toIntArray(byte[] bytes, boolean includeLength) {
        int n = (int) Math.floor((bytes.length + 3) / 4.0); // ceil(len/4)
        int[] result = new int[includeLength ? n + 1 : n];

        if (includeLength) {
            result[n] = bytes.length;
        }

        for (int i = 0; i < bytes.length; i++) {
            int wordIndex = i >>> 2;
            result[wordIndex] |= (bytes[i] & 0xFF) << ((i % 4) * 8);
        }

        return result;
    }

    /**
     * int数组转字节数组
     */
    private byte[] toByteArray(int[] ints, boolean includeLength) {
        int n = ints.length << 2;
        int length = includeLength ? ints[ints.length - 1] : n;

        if (includeLength && (length <= 0 || length > n)) {
            return new byte[0];
        }

        byte[] result = new byte[length];
        int j = 0;

        for (int i = 0; i < ints.length && j < length; i++) {
            int w = ints[i];
            for (int shift = 0; shift < 32 && j < length; shift += 8) {
                result[j++] = (byte) ((w >>> shift) & 0xFF);
            }
        }

        return result;
    }

    /**
     * Hex字符串转字节数组
     */
    private byte[] hexToBytes(String hex) {
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex字符串长度必须为偶数");
        }

        byte[] bytes = new byte[hex.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int index = i * 2;
            int value = Integer.parseInt(hex.substring(index, index + 2), 16);
            bytes[i] = (byte) value;
        }
        return bytes;
    }

    /**
     * 字节数组转Hex字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b & 0xFF));
        }
        return sb.toString();
    }

    /**
     * HMAC-SHA256签名生成
     */
    public String encodeHmacSHA256(String data, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKey);
        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }
}