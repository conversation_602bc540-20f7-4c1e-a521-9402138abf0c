package com.saida.web.service.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.saida.common.core.core.domain.model.LoginUser;
import com.saida.common.core.core.domain.model.SocialLoginBody;
import com.saida.common.core.enums.UserStatus;
import com.saida.common.core.exception.ServiceException;
import com.saida.common.core.exception.user.UserException;
import com.saida.common.core.utils.ValidatorUtils;
import com.saida.common.json.utils.JsonUtils;
import com.saida.common.security.utils.LoginHelper;
import com.saida.system.domain.vo.SysClientVo;
import com.saida.system.domain.vo.SysSocialVo;
import com.saida.system.domain.vo.SysUserVo;
import com.saida.system.service.ISysSocialService;
import com.saida.system.service.ISysUserService;
import com.saida.web.domain.vo.LoginVo;
import com.saida.web.service.IAuthStrategy;
import com.saida.web.service.SysLoginService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 第三方授权策略
 *
 * <AUTHOR> is 三三
 */
@Slf4j
@Service("social" + IAuthStrategy.BASE_NAME)
@RequiredArgsConstructor
public class SocialAuthStrategy implements IAuthStrategy {

    @Resource
    private ISysSocialService sysSocialService;
    @Resource
    private ISysUserService userService;
    @Resource
    private SysLoginService loginService;

    /**
     * 登录-第三方授权登录
     *
     * @param body   登录信息
     * @param client 客户端信息
     */
    @Override
    public LoginVo login(String body, SysClientVo client) {
        SocialLoginBody loginBody = JsonUtils.parseObject(body, SocialLoginBody.class);
        ValidatorUtils.validate(loginBody);
//        AuthResponse<AuthUser> response = SocialUtils.loginAuth(
//            loginBody.getSource(), loginBody.getSocialCode(),
//            loginBody.getSocialState(), socialProperties);
//        if (!response.ok()) {
//            throw new ServiceException(response.getMsg());
//        }
//        AuthUser authUserData = response.getData();

//        List<SysSocialVo> list = sysSocialService.selectListByAuthId(authUserData.getSource() + authUserData.getUuid());
        List<SysSocialVo> list = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("你还没有绑定第三方账号，绑定后才可以登录！");
        }
        Optional<SysSocialVo> opt = list.stream().filter(x -> x.getTenantId().equals(loginBody.getTenantId())).findAny();
        if (opt.isEmpty()) {
            throw new ServiceException("对不起，你没有权限登录当前租户！");
        }
        SysSocialVo social = opt.get();

        // 查找用户
        SysUserVo user = loadUser(social.getTenantId(), social.getUserId());

        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = loginService.buildLoginUser(user);
        loginUser.setClientKey(client.getClientKey());
        loginUser.setDeviceType(client.getDeviceType());
        SaLoginModel model = new SaLoginModel();
        model.setDevice(client.getDeviceType());
        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
        // 例如: 后台用户30分钟过期 app用户1天过期
        model.setTimeout(client.getTimeout());
        model.setActiveTimeout(client.getActiveTimeout());
        model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());
        // 生成token
        LoginHelper.login(loginUser, model);

        LoginVo loginVo = new LoginVo();
        loginVo.setAccessToken(StpUtil.getTokenValue());
        loginVo.setExpireIn(StpUtil.getTokenTimeout());
        loginVo.setClientId(client.getClientId());
        return loginVo;
    }

    private SysUserVo loadUser(Long tenantId, Long userId) {
        SysUserVo user = userService.selectTenantUserById(tenantId, userId);
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", "");
            throw new UserException("user.not.exists", "");
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", "");
            throw new UserException("user.blocked", "");
        }
        return user;
    }

}
