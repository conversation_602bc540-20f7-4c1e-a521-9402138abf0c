--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 最大连接池数量
      maximum-pool-size: 20
      # 最小空闲线程数量
      minimum-idle: 10
      # 配置获取连接等待超时的时间
      connectionTimeout: 30000
      # 校验超时时间
      validationTimeout: 5000
      # 空闲连接存活最大时间，默认10分钟
      idleTimeout: 600000
      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
      maxLifetime: 1800000
      # 多久检查一次连接的活性
      keepaliveTime: 30000
mybatis-flex:
  # sql审计
  audit_enable: true
  # sql打印
  sql_print: true
  datasource:
    # 数据源-1
    ds1:
      # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
      # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
      type: ${spring.datasource.type}
      # mysql数据库
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************************************************************************************************************************************************
      username: zhangjinchao
      password: GUHv4VrTF9dABsxQuaS7
      #postgresql数据库
#      driver-class-name: org.postgresql.Driver
#      url: ********************************************************************************************************************************************
#      username: postgres
#      password: postgres@369

# redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 2
    # 密码(如没有密码请注释掉)
    password: xX*U5SBWU=zbYDE?8zdu
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${saida.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

