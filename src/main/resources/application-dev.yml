# 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
    url: jdbc:mysql://*************:4376/gb_intall?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&autoReconnect=true&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true
    username: zhangjinchao
    password: GUHv4VrTF9dABsxQuaS7
    hikari:
      # 客户端等待连接池连接的最大毫秒数
      connection-timeout: 30000
      # 池中维护的最小空闲连接数 minIdle<0 或者 minIdle>maxPoolSize，则被重置为maxPoolSize
      minimum-idle: 10
      # 配置最大池大小
      maximum-pool-size: 65
      # 允许连接在连接池中空闲的最长时间（以毫秒为单位）
      idle-timeout: 60000
      # 池中连接关闭后的最长生命周期（以毫秒为单位）
      max-lifetime: 500000
      # 每5分钟发送一次 ping
      keepalive-time: 300000
      # 配置从池返回的连接的默认自动提交行为。默认值为true。
      auto-commit: true
      # 连接池的名称
      pool-name: VlinkerHikariCP
      # 开启连接监测泄露
      leak-detection-threshold: 5000
      # 测试连接数据库
      connection-test-query: SELECT 1

# redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 10
    # 密码(如没有密码请注释掉)
    password: xX*U5SBWU=zbYDE?8zdu
    # 连接超时时间
    timeout: 10s

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${saidaAdmin.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

wechat:
  mini-program:
    appid: wx962bbc672cb727cd
    secret: 4b7f8ae7efd4c540b2178832fc54a67d
    loginUrl: https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code

## 天翼视联授权信息
ty:
  visual:
    host: https://huiyan.ctseelink.cn
    appId: 721422041972805
    appSecret: b664cf8735834d7d8075bc9c17fef798
    version: v1.0
    clientType: 4