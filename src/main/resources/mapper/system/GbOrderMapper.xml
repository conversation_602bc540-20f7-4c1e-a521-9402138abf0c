<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saida.system.mapper.GbOrderMapper">

    <select id="getGbOrderList" parameterType="com.saida.system.domain.dto.GbOrderListDTO" resultType="com.saida.system.domain.vo.GbOrderListVO">
        SELECT
            t1.id,
            t1.order_id,
            t1.user_id,
            t1.phone,
            t1.STATUS,
            t1.create_time,
            t2.sn,
            t2.model,
            t2.NAME,
            t2.longitude,
            t2.latitude
        FROM
            gb_order t1
        LEFT JOIN (SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY id DESC) AS rn FROM gb_device) tmp WHERE rn = 1) t2 ON t1.id = t2.order_id
        <where>
            <if test="dto.userId != null">
                AND t1.user_id = #{dto.userId}
            </if>
            <if test="dto.begDateTime != null and dto.begDateTime != ''">
                AND t1.create_time >= #{dto.begDateTime}
            </if>
            <if test="dto.endDateTime != null and dto.endDateTime != ''">
                AND t1.create_time &lt; #{dto.endDateTime}
            </if>
        </where>
        order by t1.id desc
    </select>

</mapper>