<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saida.system.mapper.SysClientMapper">
    <resultMap id="BaseResultMap" type="com.saida.system.domain.SysClient">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="client_id" jdbcType="VARCHAR" property="clientId"/>
        <result column="client_key" jdbcType="VARCHAR" property="clientKey"/>
        <result column="client_secret" jdbcType="VARCHAR" property="clientSecret"/>
        <result column="grant_type" jdbcType="VARCHAR" property="grantType"/>
        <result column="device_type" jdbcType="VARCHAR" property="deviceType"/>
        <result column="active_timeout" jdbcType="INTEGER" property="activeTimeout"/>
        <result column="timeout" jdbcType="INTEGER" property="timeout"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_dept" jdbcType="BIGINT" property="createDept"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `client_id`, `client_key`, `client_secret`, `grant_type`, `device_type`, `active_timeout`, `timeout`,
        `status`, `del_flag`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`
    </sql>

</mapper>
